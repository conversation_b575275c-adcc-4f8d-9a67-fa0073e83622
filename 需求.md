# E7装备OCR助手 需求说明文档

## 项目概述

E7装备OCR助手是一个第七史诗游戏装备识别和评估系统，使用Rust+Tauri2.0+vue架构以获得最佳性能和用户体验。

## UI界面外观

### 主界面布局
应用采用左侧导航栏 + 右侧内容区域的经典布局：

**导航栏：**
- 位于界面左侧
- 包含两个导航项：
  - 装备助手 (相机图标)
  - 参数配置 (齿轮图标)
- 支持图标+文字标签显示
- 当前选中项高亮显示
- 底部有一个显示adb连接状态的图标

**内容区域：**
- 占据界面右侧主要空间
- 根据导航选择动态切换页面内容
- 使用简洁的分区布局，使用分隔线作为区分

### 装备助手页面
**整体布局：** 双列布局，左右各占50%空间
   
**左侧列：**
1. **控制区域** - 左列第一个
   - 页面标题："装备识别助手" (大号粗体字)
   - 识别按钮：蓝色主要按钮，带相机图标

2. **分数显示区域** - 左列第二个
   - 标题："装备分数"
   - 大号粗体数字显示分数 (如 "45/55")
   - 分数达标时显示绿色，不达标显示红色
   - 强化建议文本

3. **装备信息区域** - 左列第三个
   - 标题："装备信息"
   - 装备基本信息以键值对形式显示：
     - 稀有度、类型、强化等级、套装
   - 主属性和副属性列表
   - 每行左侧显示属性名，右侧显示数值

**右侧列：**
1. **图片预览区域** - 右上方
   - 标题："截图预览"
   - 显示处理后的装备截图
   - 图片自适应缩放

2. **运行日志区域** - 右下方
   - 标题："运行日志"
   - 可选择的文本区域
   - 显示日志信息
   - 支持滚动查看完整内容

### 设置页面
**整体布局：** 从上到下的垂直布局

**页面头部：**
- 标题："系统设置" (大号粗体字)
- 保存按钮：带保存图标的主要按钮
- 状态指示器：显示当前初始化状态

**设置区域（垂直排列）：**
1. **ADB设置区域**
   - 区域标题："ADB连接设置"
   - ADB端口输入框 (默认16384)
   - 连接超时设置
   - 自动连接开关
   - 连接按钮和状态显示

2. **热键设置区域**
   - 区域标题："热键设置"
   - 启用热键开关
   - 热键输入框 (如F1)

3. **OCR设置区域**
   - 区域标题："OCR识别设置"
   - 页面识别设置：边框阈值、识别阈值
   - 装备识别设置：检测阈值、边框阈值、识别阈值

4. **装备分数设置区域**
   - 区域标题："装备分数标准"
   - 左三装备分数标准表格
   - 右三装备分数标准表格
   - 速度属性分数标准表格
   - 重铸界面分数标准表格
   - 每个表格包含装备等级和对应分数标准


## UI设计说明

### 主界面布局

```
┌─────────────────────────────────────────────────────────────┐
│                    E7装备OCR助手                              │
├─────┬───────────────────────────────────────────────────────┤
│     │                                                     │
│ 📷  │                                                     │
│     │                                                     │
│ ⚙️   │              内容区域                                │
│     │           (动态切换页面)                              │
│     │                                                     │
│     │                                                     │
│     │                                                     │
│     │                                                     │
│ 🔗  │                                                     │
└─────┴───────────────────────────────────────────────────────┘
```

### 装备助手页面布局

```
┌─────────────────────────┬─────────────────────────┐
│      控制区域             │      截图预览区域          │
│                         │                         │
│ 装备识别助手 (标题)       │     截图预览              │
│ [📷 开始识别] (按钮)     │   (图片自适应显示)        │
│                         │                         │
│─────────────────────────│                         │
│      分数显示区域         │                         │
│                         │─────────────────────────│
│ 装备分数                 │      运行日志区域          │
│   45/55 (大号显示)      │                         │
│ 强化建议: 可以强化       │     运行日志              │
│                         │  [日志内容滚动显示]       │
│─────────────────────────│                         │
│      装备信息区域         │                         │
│                         │                         │
│ 装备信息                 │                         │
│ 稀有度: 传说             │                         │
│ 类型: 武器               │                         │
│ 强化: +15               │                         │
│ 套装: 攻击套装           │                         │
│ 主属性: 攻击力 525       │                         │
│ 副属性:                 │                         │
│   速度 +18             │                         │
│   暴击率 +12%          │                         │
│                         │                         │
└─────────────────────────┴─────────────────────────┘
```

### 设置页面布局

```
┌─────────────────────────────────────────────────────────────┐
│ 系统设置                    [💾 保存配置]    ●初始化完成      │
├─────────────────────────────────────────────────────────────┤
│                     ADB连接设置                              │
│                                                             │
│  ADB端口: [16384    ] 连接超时: [5000ms]                   │
│  □ 自动连接    [🔗 连接测试]  状态: ●已连接                │
│                                                             │
│─────────────────────────────────────────────────────────────│
│                     热键设置                                │
│                                                             │
│  ☑ 启用热键    热键: [F1        ]                          │
│                                                             │
│─────────────────────────────────────────────────────────────│
│                     OCR识别设置                             │
│                                                             │
│  页面识别 - 边框阈值:[0.8] 识别阈值:[0.7]                   │
│  装备识别 - 检测阈值:[0.6] 边框阈值:[0.8] 识别阈值:[0.7]    │
│                                                             │
│─────────────────────────────────────────────────────────────│
│                   装备分数标准                               │
│                                                             │
│  ┌─────────────┬────┬────┬────┬────┬────┬────┐              │
│  │ 装备类型     │+0-2│+3-5│+6-8│+9-11│+12-14│+15│              │
│  ├─────────────┼────┼────┼────┼────┼────┼────┤              │
│  │ 左三装备     │ 21 │ 28 │ 35 │ 42 │ 49 │ 55 │              │
│  │ 右三装备     │ 19 │ 26 │ 33 │ 40 │ 47 │ 53 │              │
│  │ 速度装备     │ 3  │ 6  │ 10 │ 12 │ 12 │ 14 │              │
│  └─────────────┴────┴────┴────┴────┴────┴────┘              │
│                                                             │
│  重铸标准 - 左三:[67] 右三:[65]                            │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```


## 核心功能流程

### 1. 装备识别完整流程

#### 1.1 触发识别
- 用户点击"装备识别"按钮或点击快捷键时触发
- 装备识别流程为：截图→页面识别→图像处理→装备信息提取→装备评分→显示结果

#### 1.2 设备连接与截图
1. 执行shell命令进行截图：screencap -p /sdcard/screenshot.png
2. 使用pull操作下载截图文件到src-tauri/screenshot目录
3. 返回截图文件路径供后续处理

#### 1.3 页面类型识别
页面分为背包界面、强化界面、重铸装备页面和其他页面。

**页面识别核心逻辑：**
1. 确保图形尺寸是1280*720
2. 获取截图中[y_min = 15；y_max = y_min + 35； x_min = 60；x_max = x_min + 115]的区域
3. 对该区域进行OCR识别，使用paddleocr-json参数：--det_limit_type max --det_limit_side_len 96
4. 根据识别结果判断页面类型：
   - 如果是"强化装备"则为强化装备页面
   - 如果是"背包"或"钢铁工坊"则需要后续识别
   - 如果是其他文字则为其他页面

**背包和重铸界面的二次识别：**
1. 截取截图中的[y_min = 160；y_max = y_min + 55；x_min = 25；x_max = x_min + 80]的区域
2. 与标志图像进行对比，标志在src-tauri/pages_sign目录：
   - Backpack_sign.png为背包界面标志
   - Recast_sign.png为重铸装备界面标志
3. 使用图像比对函数确定具体页面类型
4. 当识别到其他页面时直接停止识别

#### 1.4 页面图像处理
不同页面需要截取不同的装备信息区域：

**背包界面处理：**
1. 截取区域：[y_min = 165；y_max = y_min + 420； x_min = 875；x_max = x_min + 350]
2. 对截取的图片进行处理，保留以下四块区域，其余区域设置为黑色：
   - [y_min = 0；y_max = y_min + 35； x_min = 45；x_max = x_min + 45]
   - [y_min = 15；y_max = y_min + 32； x_min = 90；x_max = x_min + 80]
   - [y_min = 155；y_max = y_min + 180； x_min = 0；x_max = x_min + 350]
   - [y_min = 385；y_max = y_min + 35； x_min = 40；x_max = x_min + 200]
3. 使用paddleocr-json对处理后的图像进行OCR识别

**强化界面处理：**
1. 截取区域：[y_min = 80；y_max = y_min + 400； x_min = 30；x_max = x_min + 340]
2. 对截取的图片进行处理，保留以下四块区域，其余区域设置为黑色：
   - [y_min = 0；y_max = y_min + 35； x_min = 60；x_max = x_min + 45]
   - [y_min = 17；y_max = y_min + 23； x_min = 105；x_max = x_min + 75]
   - [y_min = 135；y_max = y_min + 215； x_min = 0；x_max = x_min + 340]
   - [y_min = 365；y_max = y_min + 35； x_min = 43；x_max = x_min + 200]
3. 使用paddleocr-json对处理后的图像进行OCR识别

**重铸界面处理：**
1. 截取区域：[y_min = 490；y_max = y_min + 135； x_min = 280；x_max = x_min + 490]
2. 对截取的图片进行处理，将[y_min = 0；y_max = y_min + 135； x_min = 370；x_max = x_min + 60]区域涂黑
3. 使用paddleocr-json对处理后的图像进行OCR识别

#### 1.5 装备信息提取

**装备基础信息：**
- 装备稀有度：传说、英雄、稀有、高级
- 装备部位：武器、头盔、铠甲、项链、戒指、鞋子
- 装备强化等级：+0到+15，装备+0时不显示数字
- 装备属性：["速度", "生命值", "防御力", "攻击力", "暴击率", "暴击伤害", "效果抗性", "效果命中"]
- 套装类型：["攻击套装", "生命值套装", "防御套装", "速度套装", "暴击套装", "破灭套装", "命中套装", "抵抗套装", "反击套装", "吸血套装", "免疫套装", "夹攻套装", "愤怒套装", "穿透套装", "憎恨套装", "伤口套装", "守护套装", "激流套装"]

**属性规则：**
- "生命值", "防御力", "攻击力"分为固定值和百分比
- 主属性和副属性不会重复
- 武器主属性一定为固定值攻击力
- 头盔主属性一定为固定值生命值
- 铠甲主属性一定为固定值防御力
- 项链戒指鞋子主属性不固定
- 装备稀有度影响副属性数量：传说级装备初始4条副属性，英雄级装备初始3条副属性在+12时获取第四条

**背包和强化界面信息提取流程：**
1. 通过正则表达式提取稀有度和部位：r"(传说|英雄|稀有|高级)(武器|头盔|铠甲|项链|戒指|鞋子)"
2. 强化等级只会在前两个文本块内出现
3. 将已使用的文本块从OCR结果中剔除
4. 将剩余文本块按行排列，y轴坐标在10以内的视作一行
5. 通过装备属性名称定位含有主属性和副属性的行
6. 从上到下第一行为主属性，其余为副属性
7. 每一行中至少有两个文本块，一个包含属性名称，另一个包含数值
8. 一个属性对应一个数值

**重铸界面信息提取：**
1. 仅需要获取主属性和副属性
2. 一个属性名称对应两个数值：从左到右第一个为重铸前数值，第二个为重铸后数值
3. 强化等级标为15
4. 标记为重铸界面

#### 1.6 装备评分系统

**分数计算公式：**
分数 = 攻击% + 生命% + 防御% + 命中 + 抵抗 + 速度 * 2 + 暴率 * (9/6) + 爆伤 * (9/8) + 攻击 * 3.46 / 39 + 防御 * 4.99 / 31 + 生命 * 3.09 / 174

**强化建议标准：**
- 武器、头盔、铠甲默认标准：21,28,35,42,49,55 对应强化等级+0-2，+3-5，+6-8，+9-11，+12-14，+15
- 项链、戒指默认标准：19,26,33,40,47,53 对应强化等级+0-2，+3-5，+6-8，+9-11，+12-14，+15
- 鞋子：主属性是速度时使用项链戒指标准，其他时使用武器头盔铠甲标准

**速度装备特殊处理：**
- 武器、头盔、铠甲、项链、戒指含有速度副属性且不满足常规标准时可作为赌速度装备
- 速度属性值标准：3,6,10,12,12,14 对应强化等级+0-2，+3-5，+6-8，+9-11，+12-14，+15

**重铸界面评分：**
- 计算重铸前后分数差
- 武器、头盔、铠甲标准为67
- 项链、戒指、鞋子标准为65

## 配置管理相关说明

### 1 识别配置文件
1. 每次启动之前从配置文件读取配置
2. 每次关闭程序时，将配置写入json文件
3. 配置文件应该位于src\config.json
4. 配置文件包含设置页面中的所有参数

### 2 配置文件结构
配置文件应包含以下主要配置项：
```json
{
  "adb_settings": {
    "port": 16384,
    "timeout": 5000,
    "auto_connect": true
  },
  "hotkey_settings": {
    "enabled": true,
    "key": "F1"
  },
  "ocr_settings": {
    "page_detection": {
      "border_threshold": 0.8,
      "recognition_threshold": 0.7
    },
    "equipment_detection": {
      "detection_threshold": 0.6,
      "border_threshold": 0.8,
      "recognition_threshold": 0.7
    }
  },
  "scoring_standards": {
    "left_side_equipment": [21, 28, 35, 42, 49, 55],
    "right_side_equipment": [19, 26, 33, 40, 47, 53],
    "speed_equipment": [3, 6, 10, 12, 12, 14],
    "recast_standards": {
      "left_side": 67,
      "right_side": 65
    }
  }
}
```

## ADB相关说明
1. 本项目是在桌面上配合安卓模拟器使用的
2. 默认模拟器为mumu模拟器，默认端口为16384
3. 本项目使用adb二进制文件或者rust库中的adb_client，请挑选耗时最少的方式

## ocr相关说明
1. 本项目采用paddleocr-json作为ocr引擎
2. 请参阅paddleocr的github官方文档，完成相关代码编写


