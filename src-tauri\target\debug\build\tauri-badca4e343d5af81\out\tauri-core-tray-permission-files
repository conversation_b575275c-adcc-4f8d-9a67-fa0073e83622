["\\\\?\\D:\\vscode\\E7\\E7gearOCR\\src-tauri\\target\\debug\\build\\tauri-badca4e343d5af81\\out\\permissions\\tray\\autogenerated\\commands\\get_by_id.toml", "\\\\?\\D:\\vscode\\E7\\E7gearOCR\\src-tauri\\target\\debug\\build\\tauri-badca4e343d5af81\\out\\permissions\\tray\\autogenerated\\commands\\new.toml", "\\\\?\\D:\\vscode\\E7\\E7gearOCR\\src-tauri\\target\\debug\\build\\tauri-badca4e343d5af81\\out\\permissions\\tray\\autogenerated\\commands\\remove_by_id.toml", "\\\\?\\D:\\vscode\\E7\\E7gearOCR\\src-tauri\\target\\debug\\build\\tauri-badca4e343d5af81\\out\\permissions\\tray\\autogenerated\\commands\\set_icon.toml", "\\\\?\\D:\\vscode\\E7\\E7gearOCR\\src-tauri\\target\\debug\\build\\tauri-badca4e343d5af81\\out\\permissions\\tray\\autogenerated\\commands\\set_icon_as_template.toml", "\\\\?\\D:\\vscode\\E7\\E7gearOCR\\src-tauri\\target\\debug\\build\\tauri-badca4e343d5af81\\out\\permissions\\tray\\autogenerated\\commands\\set_menu.toml", "\\\\?\\D:\\vscode\\E7\\E7gearOCR\\src-tauri\\target\\debug\\build\\tauri-badca4e343d5af81\\out\\permissions\\tray\\autogenerated\\commands\\set_show_menu_on_left_click.toml", "\\\\?\\D:\\vscode\\E7\\E7gearOCR\\src-tauri\\target\\debug\\build\\tauri-badca4e343d5af81\\out\\permissions\\tray\\autogenerated\\commands\\set_temp_dir_path.toml", "\\\\?\\D:\\vscode\\E7\\E7gearOCR\\src-tauri\\target\\debug\\build\\tauri-badca4e343d5af81\\out\\permissions\\tray\\autogenerated\\commands\\set_title.toml", "\\\\?\\D:\\vscode\\E7\\E7gearOCR\\src-tauri\\target\\debug\\build\\tauri-badca4e343d5af81\\out\\permissions\\tray\\autogenerated\\commands\\set_tooltip.toml", "\\\\?\\D:\\vscode\\E7\\E7gearOCR\\src-tauri\\target\\debug\\build\\tauri-badca4e343d5af81\\out\\permissions\\tray\\autogenerated\\commands\\set_visible.toml", "\\\\?\\D:\\vscode\\E7\\E7gearOCR\\src-tauri\\target\\debug\\build\\tauri-badca4e343d5af81\\out\\permissions\\tray\\autogenerated\\default.toml"]