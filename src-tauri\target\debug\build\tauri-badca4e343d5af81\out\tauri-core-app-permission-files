["\\\\?\\D:\\vscode\\E7\\E7gearOCR\\src-tauri\\target\\debug\\build\\tauri-badca4e343d5af81\\out\\permissions\\app\\autogenerated\\commands\\app_hide.toml", "\\\\?\\D:\\vscode\\E7\\E7gearOCR\\src-tauri\\target\\debug\\build\\tauri-badca4e343d5af81\\out\\permissions\\app\\autogenerated\\commands\\app_show.toml", "\\\\?\\D:\\vscode\\E7\\E7gearOCR\\src-tauri\\target\\debug\\build\\tauri-badca4e343d5af81\\out\\permissions\\app\\autogenerated\\commands\\bundle_type.toml", "\\\\?\\D:\\vscode\\E7\\E7gearOCR\\src-tauri\\target\\debug\\build\\tauri-badca4e343d5af81\\out\\permissions\\app\\autogenerated\\commands\\default_window_icon.toml", "\\\\?\\D:\\vscode\\E7\\E7gearOCR\\src-tauri\\target\\debug\\build\\tauri-badca4e343d5af81\\out\\permissions\\app\\autogenerated\\commands\\fetch_data_store_identifiers.toml", "\\\\?\\D:\\vscode\\E7\\E7gearOCR\\src-tauri\\target\\debug\\build\\tauri-badca4e343d5af81\\out\\permissions\\app\\autogenerated\\commands\\identifier.toml", "\\\\?\\D:\\vscode\\E7\\E7gearOCR\\src-tauri\\target\\debug\\build\\tauri-badca4e343d5af81\\out\\permissions\\app\\autogenerated\\commands\\name.toml", "\\\\?\\D:\\vscode\\E7\\E7gearOCR\\src-tauri\\target\\debug\\build\\tauri-badca4e343d5af81\\out\\permissions\\app\\autogenerated\\commands\\remove_data_store.toml", "\\\\?\\D:\\vscode\\E7\\E7gearOCR\\src-tauri\\target\\debug\\build\\tauri-badca4e343d5af81\\out\\permissions\\app\\autogenerated\\commands\\set_app_theme.toml", "\\\\?\\D:\\vscode\\E7\\E7gearOCR\\src-tauri\\target\\debug\\build\\tauri-badca4e343d5af81\\out\\permissions\\app\\autogenerated\\commands\\set_dock_visibility.toml", "\\\\?\\D:\\vscode\\E7\\E7gearOCR\\src-tauri\\target\\debug\\build\\tauri-badca4e343d5af81\\out\\permissions\\app\\autogenerated\\commands\\tauri_version.toml", "\\\\?\\D:\\vscode\\E7\\E7gearOCR\\src-tauri\\target\\debug\\build\\tauri-badca4e343d5af81\\out\\permissions\\app\\autogenerated\\commands\\version.toml", "\\\\?\\D:\\vscode\\E7\\E7gearOCR\\src-tauri\\target\\debug\\build\\tauri-badca4e343d5af81\\out\\permissions\\app\\autogenerated\\default.toml"]