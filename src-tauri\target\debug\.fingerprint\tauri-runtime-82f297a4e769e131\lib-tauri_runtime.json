{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 2241668132362809309, "path": 9237431349354673483, "deps": [[654232091421095663, "tauri_utils", false, 1253959213232250411], [3150220818285335163, "url", false, 15904943204174939893], [4143744114649553716, "raw_window_handle", false, 13831083175359739617], [7606335748176206944, "dpi", false, 9740914280528710929], [9010263965687315507, "http", false, 2120898470170442147], [9689903380558560274, "serde", false, 14033680660082196201], [10806645703491011684, "thiserror", false, 11363523976843617463], [12943761728066819757, "build_script_build", false, 13793681959163289186], [14585479307175734061, "windows", false, 10761100829670322292], [16362055519698394275, "serde_json", false, 9461386793466825358], [16727543399706004146, "cookie", false, 12040307648826566346]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-82f297a4e769e131\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}