{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 2241668132362809309, "path": 4933143238336490359, "deps": [[376837177317575824, "softbuffer", false, 16888209879551255552], [654232091421095663, "tauri_utils", false, 1253959213232250411], [2013030631243296465, "webview2_com", false, 12577673698900225127], [3150220818285335163, "url", false, 15904943204174939893], [3722963349756955755, "once_cell", false, 15277227264042671534], [4143744114649553716, "raw_window_handle", false, 13831083175359739617], [5986029879202738730, "log", false, 7493231253886825814], [8826339825490770380, "tao", false, 1487990883467073951], [9010263965687315507, "http", false, 2120898470170442147], [9141053277961803901, "wry", false, 8426758281033775409], [12304025191202589669, "build_script_build", false, 4244514533921553955], [12943761728066819757, "tauri_runtime", false, 11774175397241136817], [14585479307175734061, "windows", false, 10761100829670322292]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-ae2406e7b646e0da\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}