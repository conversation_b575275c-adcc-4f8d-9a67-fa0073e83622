D:\vscode\E7\E7gearOCR\src-tauri\target\debug\deps\e7gearocr_lib-416eac81c9aff6c7.d: src\lib.rs D:\vscode\E7\E7gearOCR\src-tauri\target\debug\build\e7gearocr-5730a59d901cf6b0\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7

D:\vscode\E7\E7gearOCR\src-tauri\target\debug\deps\libe7gearocr_lib-416eac81c9aff6c7.rmeta: src\lib.rs D:\vscode\E7\E7gearOCR\src-tauri\target\debug\build\e7gearocr-5730a59d901cf6b0\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7

src\lib.rs:
D:\vscode\E7\E7gearOCR\src-tauri\target\debug\build\e7gearocr-5730a59d901cf6b0\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7:

# env-dep:CARGO_PKG_AUTHORS=you
# env-dep:CARGO_PKG_DESCRIPTION=A Tauri App
# env-dep:CARGO_PKG_NAME=e7gearocr
# env-dep:OUT_DIR=D:\\vscode\\E7\\E7gearOCR\\src-tauri\\target\\debug\\build\\e7gearocr-5730a59d901cf6b0\\out
