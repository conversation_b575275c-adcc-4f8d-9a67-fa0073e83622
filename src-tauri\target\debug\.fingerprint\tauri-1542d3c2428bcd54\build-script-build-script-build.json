{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 10271200927947166497, "deps": [[654232091421095663, "tauri_utils", false, 9569721208816412028], [2021102184660760340, "tauri_build", false, 3866601496837685168], [13077543566650298139, "heck", false, 14011497238930438446], [17155886227862585100, "glob", false, 9173760382362660867]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-1542d3c2428bcd54\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}