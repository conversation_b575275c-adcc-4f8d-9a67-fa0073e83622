{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2241668132362809309, "path": 12567278105657371571, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 7320679230508651894], [3150220818285335163, "url", false, 15904943204174939893], [3191507132440681679, "serde_untagged", false, 14155764630831781258], [4071963112282141418, "serde_with", false, 11439043280020808082], [4899080583175475170, "semver", false, 13137682411090750252], [5986029879202738730, "log", false, 7493231253886825814], [6606131838865521726, "ctor", false, 15681567242682283708], [7170110829644101142, "json_patch", false, 10459651242457785988], [8319709847752024821, "uuid", false, 16276317109351330339], [9010263965687315507, "http", false, 2120898470170442147], [9451456094439810778, "regex", false, 4176540719156029849], [9556762810601084293, "brotli", false, 9389967630198371194], [9689903380558560274, "serde", false, 14033680660082196201], [10806645703491011684, "thiserror", false, 11363523976843617463], [11989259058781683633, "dunce", false, 7676748085225527595], [13625485746686963219, "anyhow", false, 6873558770318121812], [15609422047640926750, "toml", false, 8468006712169672750], [15622660310229662834, "walkdir", false, 63268157409693744], [15932120279885307830, "memchr", false, 5562591800248901408], [16362055519698394275, "serde_json", false, 9461386793466825358], [17146114186171651583, "infer", false, 15133150024843578260], [17155886227862585100, "glob", false, 12301232751996332556], [17186037756130803222, "phf", false, 6429359731572939834]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-a7829efce49abbbc\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}