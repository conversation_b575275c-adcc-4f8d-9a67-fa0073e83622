["\\\\?\\D:\\vscode\\E7\\E7gearOCR\\src-tauri\\target\\debug\\build\\tauri-badca4e343d5af81\\out\\permissions\\path\\autogenerated\\commands\\basename.toml", "\\\\?\\D:\\vscode\\E7\\E7gearOCR\\src-tauri\\target\\debug\\build\\tauri-badca4e343d5af81\\out\\permissions\\path\\autogenerated\\commands\\dirname.toml", "\\\\?\\D:\\vscode\\E7\\E7gearOCR\\src-tauri\\target\\debug\\build\\tauri-badca4e343d5af81\\out\\permissions\\path\\autogenerated\\commands\\extname.toml", "\\\\?\\D:\\vscode\\E7\\E7gearOCR\\src-tauri\\target\\debug\\build\\tauri-badca4e343d5af81\\out\\permissions\\path\\autogenerated\\commands\\is_absolute.toml", "\\\\?\\D:\\vscode\\E7\\E7gearOCR\\src-tauri\\target\\debug\\build\\tauri-badca4e343d5af81\\out\\permissions\\path\\autogenerated\\commands\\join.toml", "\\\\?\\D:\\vscode\\E7\\E7gearOCR\\src-tauri\\target\\debug\\build\\tauri-badca4e343d5af81\\out\\permissions\\path\\autogenerated\\commands\\normalize.toml", "\\\\?\\D:\\vscode\\E7\\E7gearOCR\\src-tauri\\target\\debug\\build\\tauri-badca4e343d5af81\\out\\permissions\\path\\autogenerated\\commands\\resolve.toml", "\\\\?\\D:\\vscode\\E7\\E7gearOCR\\src-tauri\\target\\debug\\build\\tauri-badca4e343d5af81\\out\\permissions\\path\\autogenerated\\commands\\resolve_directory.toml", "\\\\?\\D:\\vscode\\E7\\E7gearOCR\\src-tauri\\target\\debug\\build\\tauri-badca4e343d5af81\\out\\permissions\\path\\autogenerated\\default.toml"]