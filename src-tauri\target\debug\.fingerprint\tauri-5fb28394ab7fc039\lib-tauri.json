{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 2241668132362809309, "path": 9024908729764251435, "deps": [[40386456601120721, "percent_encoding", false, 11221796395885637519], [654232091421095663, "tauri_utils", false, 1253959213232250411], [1200537532907108615, "url<PERSON><PERSON>n", false, 7320679230508651894], [1967864351173319501, "muda", false, 11821226234145674283], [2013030631243296465, "webview2_com", false, 12577673698900225127], [3150220818285335163, "url", false, 15904943204174939893], [3331586631144870129, "getrandom", false, 10301563155890269113], [4143744114649553716, "raw_window_handle", false, 13831083175359739617], [4919829919303820331, "serialize_to_javascript", false, 3461549601078990756], [5986029879202738730, "log", false, 7493231253886825814], [9010263965687315507, "http", false, 2120898470170442147], [9689903380558560274, "serde", false, 14033680660082196201], [10229185211513642314, "mime", false, 15096360362144294029], [10806645703491011684, "thiserror", false, 11363523976843617463], [11989259058781683633, "dunce", false, 7676748085225527595], [12092653563678505622, "build_script_build", false, 15747743202315099022], [12304025191202589669, "tauri_runtime_wry", false, 11559872919294213889], [12565293087094287914, "window_vibrancy", false, 7099353973675050593], [12943761728066819757, "tauri_runtime", false, 11774175397241136817], [12986574360607194341, "serde_repr", false, 14324535416803695765], [13077543566650298139, "heck", false, 16248370270204633944], [13405681745520956630, "tauri_macros", false, 14081488863432232880], [13625485746686963219, "anyhow", false, 6873558770318121812], [14585479307175734061, "windows", false, 10761100829670322292], [16362055519698394275, "serde_json", false, 9461386793466825358], [16928111194414003569, "dirs", false, 2369721214217924604], [17155886227862585100, "glob", false, 12301232751996332556], [17531218394775549125, "tokio", false, 14137825425677889510]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-5fb28394ab7fc039\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}