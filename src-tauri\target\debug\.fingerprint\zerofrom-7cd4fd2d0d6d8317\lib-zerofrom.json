{"rustc": 1842507548689473721, "features": "[\"alloc\", \"derive\"]", "declared_features": "[\"alloc\", \"default\", \"derive\"]", "target": 723370850876025358, "profile": 2241668132362809309, "path": 284313528032684795, "deps": [[4022439902832367970, "zerofrom_derive", false, 9785691387452042627]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zerofrom-7cd4fd2d0d6d8317\\dep-lib-zerofrom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}